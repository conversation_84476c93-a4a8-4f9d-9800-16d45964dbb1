<template>
  <div class="app-container">
    <Cards>
      <div
          class="book-info-container"
          :style="isShow ? '' : 'margin-bottom:0;'"
      >
        <div class="book-info-left" :style="isShow ? '' : 'height:auto;'">
          <img
              :src="
              bookInfo.cover ||
              'http://dutp-test.oss-cn-beijing.aliyuncs.com/1741232140222.png'
            "
              alt="Book Cover"
              v-if="isShow"
          />
        </div>
        <div
            class="book-info-right"
            :style="isShow ? '' : 'margin-left:-200px'"
        >
          <div class="book-info-title">
            <div class="book-info-title-left">
              <h2>{{ bookInfo.bookName }}</h2>
              <div class="book-info-tags">
                <el-tag
                    type="info"
                    color="#FBEFDA"
                    style="margin-right: 8px; color: #e37318"
                >公开教材</el-tag
                >

                <el-tag
                    type="info"
                    color="#EAEEF2"
                    v-if="
                    bookInfo.bookOrganize == 1 && bookInfo.publishStatus == 1
                  "
                    style="margin-right: 8px; color: #007dff"
                >未出版</el-tag
                >
                <el-tag
                    type="info"
                    color="#FFEFD8"
                    v-if="
                    bookInfo.bookOrganize == 1 && bookInfo.publishStatus == 2
                  "
                    style="margin-right: 8px; color: #ff9600"
                >已出版</el-tag
                >
                <el-tag
                    type="info"
                    color="#FFEFD8"
                    v-if="
                    bookInfo.bookOrganize == 2 && bookInfo.publishStatus == 1
                  "
                    style="margin-right: 8px; color: #ff9600"
                >已创建</el-tag
                >
                <el-tag
                    type="info"
                    color="#DEF0FF"
                    v-if="
                    bookInfo.bookOrganize == 2 && bookInfo.publishStatus == 2
                  "
                    style="margin-right: 8px; color: #008bff"
                >已完成</el-tag
                >

                <el-tag
                    type="info"
                    color="#EAEEF1"
                    v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 1"
                    style="margin-right: 8px; color: #008ceb"
                >已上架</el-tag
                >
                <el-tag
                    type="info"
                    color="#EAEEF1"
                    v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 2"
                    style="margin-right: 8px; color: #008ceb"
                >待上架</el-tag
                >
                <el-tag
                    type="info"
                    color="#EAEEF1"
                    v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 3"
                >已召回</el-tag
                >
                <el-tag
                    type="info"
                    color="#E4F1ED"
                    v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 4"
                    style="margin-right: 8px; color: #00eba4"
                >即将上架</el-tag
                >
              </div>
            </div>
          </div>
          <div class="book-info" v-if="isShow">
            <div class="book-info-price" v-if="bookInfo.bookOrganize == 1">
              <div class="book-info-price-discount">
                {{ bookInfo.priceSale }}
              </div>
              <div class="book-info-price-original">
                {{ bookInfo.priceCounter }}
              </div>
            </div>

            <div class="book-info-description">
              <div class="book-info-description-left">
                <div class="book-info-description-item">
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label"
                    >教材编号</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.bookNo
                    }}</span>
                  </div>
                  <div
                      class="book-info-description-item-panel"
                      v-if="
                      (bookInfo.isbn || !bookInfo.issn) &&
                      bookInfo.bookOrganize == 1
                    "
                  >
                    <span class="book-info-description-item-label">ISBN</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.isbn
                    }}</span>
                  </div>
                  <div
                      class="book-info-description-item-panel"
                      v-if="bookInfo.issn && bookInfo.bookOrganize == 1"
                  >
                    <span class="book-info-description-item-label">ISSN</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.issn
                    }}</span>
                  </div>
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label">版次</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.edition
                    }}</span>
                  </div>
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label">版本号</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.versionNo
                    }}</span>
                  </div>
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label">选题号</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.topicNo
                    }}</span>
                  </div>
                </div>
                <div class="book-info-description-item">
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label"
                    >主/副教材</span
                    ><span class="book-info-description-item-value">{{
                      getOptionDesc(bookNatureTypeList, bookInfo.masterFlag)
                    }}</span>
                  </div>
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label">语种</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.languageName
                    }}</span>
                  </div>
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label"
                    >出版社单位</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.houseName
                    }}</span>
                  </div>
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label"
                    >出版时间</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.publishDate
                    }}</span>
                  </div>
                  <div class="book-info-description-item-panel">
                    <span class="book-info-description-item-label"
                    >创建时间</span
                    ><span class="book-info-description-item-value">{{
                      bookInfo.createTime
                    }}</span>
                  </div>
                </div>
              </div>
              <div class="book-info-description-right">
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">学校</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.schoolName
                  }}</span>
                </div>

                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">关联教材</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.deputyBookName
                  }}</span>
                </div>
              </div>
            </div>

            <div class="book-tools">
              <div class="book-tool-item">
                <div class="book-tool-item-label">完成度</div>
                <div class="book-tool-item-value">
                  <el-progress
                      :percentage="bookInfo.completeRate"
                      style="width: 270px"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Cards>
    <Cards>
      <el-tabs v-model="activeName">
        <el-tab-pane label="章节目录" name="basic1">
          <chapter
              :bookId="bookId"
              @refresh="refresh"
              :publish-status="bookInfo.publishStatus"
              :step-id="bookInfo.stepId"
              :isFree="checkIsZero"
              :masterFlag="bookInfo.masterFlag"
              :bookOrganize="bookInfo.bookOrganize"
              :auditState="bookInfo.auditState"
          />
        </el-tab-pane>
      </el-tabs>
    </Cards>
  </div>
</template>
<script setup name="BookDetail">
import {
  getBook,
} from "@/api/book/book.js";
import chapter from "./components/shareChapter.vue";
import { bookNatureTypeList, getOptionDesc } from "@/utils/optionUtil.js";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const bookInfo = ref({
  stepId: "1",
});
const bookId = ref();
const activeName = ref("basic1");
const isShow = ref(true);

const checkIsZero = computed(() => {
  return Number(bookInfo.value.priceSale || 0) == 0;
});

//#region  生命周期相关
onActivated(() => {
  getBookDetail();
  activeName.value = "basic1";
});

onMounted(() => {
  getBookDetail();
});

// 刷新数据
function refresh() {
  getBookDetail();
}

// 获取教材详情
function getBookDetail() {
  bookId.value = route.query.bookId;
  getBook(bookId.value).then((response) => {
    bookInfo.value = response.data;
    if (bookInfo.value.masterBookName) {
      bookInfo.value.deputyBookName = bookInfo.value.masterBookName;
    }
  });
}
</script>
<style scoped lang="scss">
.worn-text {
  margin-top: 10px;
  color: #FF6262;
  background-color: #FFEDED;
  padding: 10px;
  border: 2px solid #FFC8C8;
  border-radius: 5px;
}
.book-info-container {
  display: grid;
  grid-template-columns: 196px 1fr;
  margin-bottom: 20px;
  padding: 30px;

  .book-info-left {
    width: 165px;
    height: 230px;
    margin-right: 31px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .book-info-right {
    .book-info-title {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .book-info-title-left {
        display: flex;
        align-items: flex-start;

        h2 {
          font-size: 20px;
          font-weight: 400;
          margin: 0;
        }

        .book-info-tags {
          display: flex;
          margin-left: 15px;

          .el-tag {
            border: none;
          }

          span {
            margin-right: 5px;
          }
        }
      }

      .book-info-title-right {
        .book-info-title-btn {
          display: flex;
          justify-content: flex-end;
        }
      }
    }

    .book-info-price {
      display: flex;
      align-items: center;
      margin-top: 11px;

      .book-info-price-discount {
        font-size: 20px;
        color: #dc0808;
        line-height: 28px;

        &::before {
          content: "￥";
          font-size: 14px;
          color: #dc0808;
        }
      }

      .book-info-price-original {
        font-size: 14px;
        color: #999;
        line-height: 28px;
        text-decoration-line: line-through;
        margin-left: 10px;

        &::before {
          content: "￥";
          font-size: 14px;
          color: #999;
          text-decoration-line: none;
        }
      }
    }

    .book-info-description {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 30px;

      .book-info-description-left {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 30px;

        .book-info-description-item {
          line-height: 30px;

          .book-info-description-item-panel {
            .book-info-description-item-label {
              color: #999;
              margin-right: 14px;
              display: inline-block;
              min-width: 56px;
              font-size: 14px;
            }

            .book-info-description-item-value {
              display: inline-block;
              color: #333;
              font-size: 14px;
            }
          }
        }
      }

      .book-info-description-right {
        line-height: 28px;

        .book-info-description-item-panel {
          display: grid;
          grid-template-columns: 56px 1fr;

          .book-info-description-item-label {
            color: #999;
            margin-right: 14px;
            display: inline-block;
            min-width: 56px;
            font-size: 14px;
          }

          .book-info-description-item-value {
            display: inline-block;
            color: #333;
            font-size: 14px;
            margin-left: 14px;
          }
        }
      }
    }

    .book-tools {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      margin-top: 37px;

      .book-tool-item {
        display: flex;
        align-items: center;

        .book-tool-item-label {
          font-size: 14px;
          font-weight: bold;
        }

        .book-tool-item-value {
          margin-left: 25px;
        }
      }
    }
  }
}
</style>
