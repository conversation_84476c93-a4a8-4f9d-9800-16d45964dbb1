<!-- @Author: <PERSON><PERSON><PERSON><PERSON> -->
<template>
  <div class="app-container">
    <Cards>
      <div class="tool">
        <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            label-width="100px"
        >
          <el-form-item label="教材名称：" prop="bookName">
            <el-input
                v-model="queryParams.bookName"
                placeholder="请输入教材名称"
                clearable
                maxlength="30"
                @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="教材编号：" prop="bookNo">
            <el-input
                v-model="queryParams.bookNo"
                placeholder="请输入教材编号"
                clearable
                maxlength="20"
                @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item
              label="ISBN："
              prop="isbn"
          >
            <el-input
                v-model="queryParams.isbn"
                placeholder="请输入ISBN"
                clearable
                maxlength="40"
                @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item
              label="ISSN："
              prop="issn"
          >
            <el-input
                v-model="queryParams.issn"
                placeholder="请输入ISSN"
                clearable
                maxlength="40"
                @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="出版单位：" prop="houseId">
            <el-select
                v-model="queryParams.houseId"
                placeholder="全部"
                clearable
                filterable
                style="width: 240px"
            >
              <el-option
                  v-for="item in publishHouselList"
                  :key="item.houseId"
                  :label="item.houseName"
                  :value="item.houseId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="学校：" prop="schoolId">
            <el-select
                v-model="queryParams.schoolId"
                placeholder="全部"
                clearable
                filterable
                style="width: 240px"
            >
              <el-option
                  v-for="item in schoolList"
                  :key="item.schoolId"
                  :label="item.schoolName"
                  :value="item.schoolId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="教材状态：" prop="publishStatus">
            <el-select
                v-model="queryParams.publishStatus"
                placeholder="全部"
                clearable
                style="width: 240px"
            >
              <el-option
                  v-for="item in bookPublishStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="节点：" prop="stepId">
            <el-select
                v-model="queryParams.stepId"
                placeholder="全部"
                clearable
                style="width: 240px"
            >
              <el-option
                  v-for="item in publishSteplList"
                  :key="item.stepId"
                  :label="item.stepName"
                  :value="item.stepId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                plain
                icon="Plus"
            >分享记录
            </el-button>
          </el-col>
          <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </div>
    </Cards>
    <Cards class="top20">
      <el-table
          v-loading="loading"
          :data="bookList"
          :header-cell-style="{
        background: '#EDF4FD !important',
        color: '#666666',
        textAlign: 'center',
      }"
          row-key="bookId"
          :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column
            label="教材编号"
            align="center"
            prop="bookNo"
            width="220"
            fixed="left"
        >
        </el-table-column>

        <el-table-column
            label="教材名称"
            align="center"
            prop="bookName"
            width="150"
            show-overflow-tooltip
            fixed="left"
        />
        <el-table-column label="封面" align="center" prop="cover" width="100">
          <template #default="scope">
            <imagePreview :src="scope.row.cover || 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1741232140222.png' " width="60px" height="80px" />
          </template>
        </el-table-column>
        <el-table-column
            label="ISBN"
            align="center"
            prop="isbn"
        />
        <el-table-column
            label="ISSN"
            align="center"
            prop="issn"
        />
        <el-table-column
            label="教材状态"
            align="center"
            prop="publishStatus"
            width="80"
        >
          <template #default="scope">
            <el-tag>{{
                getOptionDesc(bookPublishStatusOptions, scope.row.publishStatus)
              }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="节点" align="center" prop="stepName" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.auditState == null || scope.row.auditState == 3" type="success">制作中</el-tag>
            <el-tag v-else type="success">{{ scope.row.stepName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="销售状态"
            align="center"
            prop="shelfState"
            width="100"
        >
          <template #default="scope">
            <div v-if="scope.row.currentVersionId != scope.row.lastVersionId">
              <el-tag  type="warning"> 未上架 </el-tag>
            </div>
            <el-tag v-else  type="warning">{{
                getOptionDesc(bookShelfStatusOptions, scope.row.shelfState)
              }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="主/副教材" align="center" prop="masterFlag">
          <template #default="scope">
            <el-tag type="info">{{
                getOptionDesc(bookNatureTypeList, scope.row.masterFlag)
              }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="关联教材"
            align="center"
            prop="deputyBookName"
            width="100"
            show-overflow-tooltip
        />
        <el-table-column
            width="100"
            label="定价（元）"
            align="center"
            prop="priceCounter"
        />
        <el-table-column
            label="售价（元）"
            width="100"
            align="center"
            prop="priceSale"
        >
          <template #default="scope">
            {{ scope.row.priceSale || (scope.row.masterFlag == 3 ? '0' : '')}}
          </template>
        </el-table-column>
        <el-table-column label="出版单位" align="center" prop="houseName" />
        <el-table-column
            label="学校"
            align="center"
            width="100"
            show-overflow-tooltip
            prop="schoolName"
        />

        <el-table-column label="操作" align="center" width="250" fixed="right">
          <template #default="scope">
            <el-button
                link
                type="primary"
                icon="View"
                @click="handleShare(scope.row.bookId)"
                v-hasPermi="['book:book:view']"
            >
              分享
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="page.pageNum"
          v-model:limit="page.pageSize"
          @pagination="getList()"
      />
    </Cards>
  </div>
</template>

<script setup>
/* import*/
import {  listBook } from "@/api/book/book.js";
import {
  bookNatureTypeList,
  bookShelfStatusOptions,
  bookPublishStatusOptions,
  getOptionDesc,
} from "@/utils/optionUtil.js";
import { listStepNotPage } from "@/api/book/bookPublishStep.js";
import { listSchoolNoPage } from "@/api/basic/school.js";
import { listNoPage as listPublishHouse } from "@/api/basic/house.js";
const { proxy } = getCurrentInstance();
/* data*/
const loading = ref(true);
const bookList = ref([]);
const total = ref(0);
const publishHouselList = ref([]);
const publishSteplList = ref([]);
const schoolList = ref([]);
const showSearch = ref(true);

const page = ref({
  pageNum: 1,
  pageSize: 10,
});

const data = reactive({
  form: {
    authorLabel: "作者",
    deputyBookList: [],
  },
  queryParams: {
    bookName: null,
    cover: null,
    ibsn: null,
    bookNo: null,
    publishDate: null,
    publishOrganization: null,
    publishStatus: null,
    schoolId: null,
    bookType: null,
    extBookId: null,
    writerId: null,
    soldQuantity: null,
    readQuantity: null,
    priceCounter: null,
    priceSale: null,
    topicNo: null,
    shelfTime: null,
    unshelfTime: null,
    shelfState: null,
    sort: null,
    bookOrganize: 1,
  },
});

const { queryParams, form } = toRefs(data);
/* methods*/
// 获取学校
function getSchoolList() {
  listSchoolNoPage().then((res) => {
    schoolList.value = res.data;
  });
}

// 获取节点
function getPublishStepList() {
  listStepNotPage().then((res) => {
    publishSteplList.value = res.data;
  });
}

// 获取出版社列表
function getPublishHouseList() {
  listPublishHouse().then((res) => {
    publishHouselList.value = res.data;
  });
}

// 获取教材列表
async function getList() {
  loading.value = true;
  let response = await listBook({
    ...queryParams.value,
    ...page.value,
  });
  bookList.value = response.rows;
  total.value = response.total;
  loading.value = false;
}

// 分享教材弹窗
function handleShare(bookId) {
  proxy.$router.push({
    path: "/book/shareEditor",
    query: { bookId: bookId },
  });
}

onMounted(() => {
  getSchoolList();
  getPublishStepList();
  getPublishHouseList();
  getList();
});

// 搜索按钮操作
function handleQuery() {
  getList();
}

// 重置按钮操作
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}


</script>
<style lang="scss" scoped>
.tool {
  padding: 20px 0 0;
}

.top20 {
  margin-top: 20px;
}

.pagination {
  float: right;
  margin: 20px 0;
}
</style>