<template>
  <div class="tree-list">
    <div v-for="item in innerDataList" :key="item.id" class="tree-item">
      <div class="tree-header">
        <div class="tree-checkbox">
          <el-icon
            style="margin-right: 5px"
            @click="handleShowToggle(item)"
            v-if="item.children?.length"
          >
            <ArrowDownBold v-if="!item.isOpen" />
            <ArrowUpBold v-else />
          </el-icon>
          <el-checkbox
            v-if="item.parentId == 0"
            v-model="item.checked"
            :style="{ marginLeft: item.children?.length ? '' : '20px' }"
          ></el-checkbox>
        </div>
        <div class="tree-title">
          <div class="tree-name">{{ item.name }}</div>
          <div class="tree-tag" v-if="item.parentId == 0">
            <el-tag round type="warning" v-if="item.free == 2" size="large"
            >可试读</el-tag
            >
            <el-tag round type="info" v-if="item.free == 1" size="large"
            >不可试读</el-tag
            >

            <el-tag
                round
                type="primary"
                v-if="item.chapterStatus == 1"
                size="large"
            >已提交</el-tag
            >
            <el-tag
                round
                type="success"
                v-if="item.chapterStatus == 2"
                size="large"
            >已通过</el-tag
            >
            <el-tag
                round
                type="danger"
                v-if="item.chapterStatus == 3"
                size="large"
            >已驳回</el-tag
            >
          </div>
        </div>
      </div>
      <div class="tree-content" v-if="item.parentId == 0">
        <div class="tree-author">编写者:{{ item.editor }}</div>
        <div class="tree-buttons">
          <el-button
            type="primary"
            size="large"
            text
            @click="handlePreview(item)"
          >
            <template #default>
              <i class="iconfont-edit iconfont-search" />
              阅读器预览
            </template>
          </el-button>
        </div>
      </div>
      <div class="tree-line" v-if="item.parentId == 0">
        <span style="margin-right: 10px">完成度</span>
        <el-progress :percentage="item.completeRate" style="width: 270px" />
      </div>
      <TreeNode
        v-if="item.isOpen && item.children && item.children.length > 0"
        :dataList="item.isOpen ? item.children : []"
      />
    </div>


    <el-dialog
      v-model="completeRateDialogVisible"
      align-center
      destroy-on-close
      title="设置进度"
      width="500"
    >
      <div class="tree-setting">
        <div class="tree-setting-title">
          <span>当前设置进度值:</span><strong>{{ form.completeRate }}%</strong>
        </div>
        <div style="padding: 20px">
          <el-slider v-model="form.completeRate" :min="0" :max="100" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="completeRateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCompleteRateSave">
            设置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="rejectedDialogVisible"
      align-center
      destroy-on-close
      title="驳回原因"
      width="500"
    >
      <div class="tree-setting">
        <div class="tree-setting-title">
          <span style="font-weight: bold">{{ form.chapterName }}</span>
        </div>
        <div class="tree-setting-title">
          <span style="color: red">{{
            form.auditType == 1 ? "章节提交申请已驳回" : "章节撤销申请已驳回"
          }}</span>
        </div>
        <div
          class="tree-setting-title"
          style="background-color: #eef5fd; padding: 10px"
        >
          <span>{{ form.remark }}</span>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-model="revokedDialogVisible"
      align-center
      destroy-on-close
      title="撤销"
      width="500"
    >
      <div style="margin-bottom: 10px">撤销理由：</div>
      <el-input
        type="textarea"
        placeholder="请输入内容"
        :maxlength="200"
        show-word-limit
        :autosize="{ minRows: 4, maxRows: 6 }"
        v-model="form.revokedReason"
      >
      </el-input>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="revokedDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCompleteRevoked">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="章节导入"
      v-model="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".docx"
        :headers="upload.headers"
        :action="upload.url + '?chapterId=' + upload.chapterId"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入docx格式文件。</span>
            <div class="worn-text">
              文件内，第一行必须是一级标题， 一级标题只能有一个。
            </div>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TreeNode">
import { updateChapter } from "@/api/book/chapter";
import {
  queryRejectedReasonInfo,
  revokedChapterApply,
} from "@/api/book/bookChapterAuditLog";
import { getToken } from "@/utils/auth";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowUpBold, ArrowDownBold } from "@element-plus/icons-vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  dataList: {
    type: Array,
    required: true,
  },
  isFree: {
    type: Boolean,
    default: false,
  },
  masterFlag: {
    type: Number,
    default: 1,
  },
  bookOrganize: {
    type: Number,
    default: 1,
  },
  auditState: {
    type: Number,
    default: null,
  }
});
const emit = defineEmits(["refresh", "editChapter"]);

const form = ref({});
const completeRateDialogVisible = ref(false);
const rejectedDialogVisible = ref(false);
const revokedDialogVisible = ref(false);
const innerDataList = ref(props.dataList || []);
const route = useRoute();
const router = useRouter();
const upload = reactive({
  open: false,
  isUploading: false,
  chapterId: null,
  headers: {
    Authorization: "Bearer " + getToken(),
    Language: sessionStorage.getItem("Language"),
  },
  url: import.meta.env.VITE_APP_BASE_API + "/book/chapter/importChapter",
});

//#region 监听器相关

// TODO bug 监听不到二级数据
watch(
  () => props.dataList,
  (value) => {
    innerDataList.value = value;
  },
  {
    deep: true,
  }
);


// 预览
function handlePreview(item) {
  const chapterId = item.chapterId;
  const bookId = item.bookId;
  window.open(
    `${
      import.meta.env.VITE_READER_PREVIEW_URL +
      "?k=" +
      bookId +
      "&cid=" +
      chapterId
    }`
  );
}

// 刷新数据
function refresh() {
  emit("refresh");
}

// 切换展示展示子菜单
function handleShowToggle(item) {
  item.isOpen = !item.isOpen;
}

// 对外暴露选中结果
function handleSelect() {
  let selectdataList = innerDataList.value.filter((item) => item.checked);
  return selectdataList;
}

// 对外暴露全选
function handleCheckAll() {
  innerDataList.value.forEach((item) => {
    item.checked = true;
  });
}

// 对外暴露取消全选
function handleCancelCheckAll() {
  innerDataList.value.forEach((item) => {
    item.checked = false;
  });
}
//#endregion

//#region 暴露函数相关

defineExpose({
  handleSelect,
  handleCheckAll,
  handleCancelCheckAll,
});

//#endregion
</script>

<style scoped lang="scss">
.worn-text {
  margin-top: 10px;
  color: #FF6262;
  background-color: #FFEDED;
  padding: 10px;
  border: 2px solid #FFC8C8;
  border-radius: 5px;
}
.tree-list {
  .tree-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .tree-title {
      font-size: 16px;
      font-weight: bold;
      padding-left: 20px;
      display: flex;
      align-items: center;

      .tree-name {
        padding-right: 20px;
      }

      .tree-tag {
        span {
          margin-right: 10px;
        }
      }
    }
  }

  .tree-content {
    display: flex;
    justify-content: space-between;
    font-size: 14px;

    .tree-author {
      color: #666;
      padding-left: 50px;
    }

    .tree-buttons {
      .iconfont-edit {
        display: inline-flex;
        width: 16px;
        height: 16px;
        background: url("@/assets/images/edit.svg") no-repeat;
        background-size: contain;
        margin-right: 5px;
      }

      .iconfont-import {
        background: url("@/assets/images/import.svg") no-repeat;
      }

      .iconfont-search {
        background: url("@/assets/images/search.svg") no-repeat;
      }

      .iconfont-submit {
        background: url("@/assets/images/submit.svg") no-repeat;
      }

      .iconfont-editer {
        background: url("@/assets/images/editer.svg") no-repeat;
      }

      .iconfont-print {
        background: url("@/assets/images/print.svg") no-repeat;
      }

      .iconfont-cancel {
        background: url("@/assets/images/cancel.svg") no-repeat;
      }
    }
  }

  .tree-item {
    padding: 20px 40px;

    &:hover {
      background: #f6f6f6;

      cursor: pointer;
    }
  }

  .tree-line {
    font-size: 14px;
    display: flex;
    padding-left: 50px;

    .tree-set {
      margin-left: 27px;
      color: #0966b4;
      cursor: pointer;
    }

    .demo-progress .el-progress--line {
      margin-bottom: 15px;
      max-width: 600px;
    }
  }

  .tree-setting {
    .tree-setting-title {
      color: #999;

      span {
        color: #666;
      }

      strong {
        font-size: 16px;
        padding: 0 3px;
      }

      margin-bottom: 10px;
    }
  }
}
</style>
