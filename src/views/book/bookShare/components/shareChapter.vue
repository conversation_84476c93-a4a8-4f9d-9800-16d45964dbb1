<template>
  <div class="app-container">
    <div class="chapter-header">
      <div class="chapter-header-main">
        <div>
          <el-checkbox size="large" v-model="selectAll" :disabled="dataList.length == 0"
            @change="handleSelectAll">全部章节</el-checkbox>
        </div>
      </div>
      <div>
        <el-button type="primary" @click="openShareDialog" :icon="Tools">分享</el-button>
      </div>
    </div>

    <div class="chapter-list" v-if="dataList.length > 0">
      <TreeNode :dataList="dataList" ref="treeNode"
        :isFree="props.isFree" :masterFlag="masterFlag" :bookOrganize="bookOrganize" />
    </div>
    <div class="chapter-list" v-else>
      <el-empty description="暂无数据" />
    </div>
    <!-- 分享 -->
    <el-dialog title="新增分享" v-model="isShare" width="50%" style="top: 20%">
      <div>
        <el-form :model="shareForm" label-width="150px">
          <el-row :gutter="12">
            <el-col :offset="2">
              <div><el-text tag="b" size="large">分享链接:</el-text></div>
              <el-link type="primary" underline="always">http://localhost:81/book/bookShare/{{ shareForm.shareId }}</el-link>
            </el-col>

          </el-row>

          <el-row :gutter="20" class="mt20">
            <el-col :offset="2">
              <div><el-text tag="b" size="large">成员访问有效期:</el-text></div>
              <el-radio-group v-model="shareForm.validityPeriod" style="align-items: flex-start;display: flex;flex-direction: column;">
                <el-radio value="1" size="large">永久有效</el-radio>
                <el-radio value="2" size="large">7天</el-radio>
                <el-radio value="3" size="large">1天</el-radio>
                <el-radio value="4" size="large">自定义</el-radio>
              </el-radio-group>
              <el-date-picker
                  v-if="shareForm.validityPeriod == '4'"
                  clearable
                  unlink-panels
                  v-model="shareForm.time"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDetailDateChange">
              </el-date-picker>
            </el-col>
          </el-row>

          <el-row :gutter="12" class="mt20">
            <el-col :offset="2">
              <div><el-text tag="b" size="large">阅读码:</el-text></div>
              <el-switch
                  v-model="isReadCode"
                  class="ml-2"
                  style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              />
            </el-col>
          </el-row>

          <el-row :gutter="12" class="mt20">
            <el-col :offset="2">
                <el-input
                    v-model="shareForm.code"
                    :maxlength="10"
                    style="width: 350px"
                    placeholder="请输入阅读码"
                />
            </el-col>
          </el-row>

        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isShare = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmShare">
            确认分享
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="book-detail-chapter">
import {
  listChapter,
} from "@/api/book/chapter.js";
import TreeNode from "./TreeNode.vue";
import {CopyDocument, Delete, Discount, Plus, Sort, Tools} from "@element-plus/icons-vue";
import {uuid} from "@/utils/index.js";

const { proxy } = getCurrentInstance();
const props = defineProps({
  bookId: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["refresh"]);
const loading = ref(false);
const showPreviewUrl = ref(false);
const selectAll = ref(false);
const dataList = ref([]);
const isShare = ref(false);
const isReadCode = ref(false);
const shareForm = ref({
  shareId:"",
  validityPeriod:"1"
});
//#region 监听器相关

watch(
  () => props.bookId,
  () => {
    getChapters();
  }
);

function openShareDialog(){
  shareForm.value.shareId = uuid();
  isShare.value = true;
}

function handleConfirmShare(){

}

// 获取章节列表
function getChapters() {
  loading.value = true;
  listChapter({ bookId: props.bookId }).then((response) => {
    dataList.value = response.data;
    loading.value = false;
  });
}


// 全选
function handleSelectAll() {
  if (selectAll.value) {
    proxy.$refs["treeNode"].handleCheckAll();
  } else {
    proxy.$refs["treeNode"].handleCancelCheckAll();
  }
}


//#endregion
</script>
<style lang="scss">
.list-group {
  width: 100%;

  .list-group-item {
    width: 100%;
    display: flex;

    align-items: center;
    margin: 10px 0;
    padding: 15px 30px;
    background-color: #0966b4;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;

    &:hover {
      background-color: hsl(207, 100%, 50%);
    }

    &::before {
      content: "";
      width: 20px;
      height: 20px;
      margin-right: 10px;
      background-image: url("@/assets/images/move.svg");
      background-size: cover;
    }
  }
}
</style>
<style lang="scss" scoped>
.chapter {
  .chapter-header {
    padding: 20px 30px;
    background-color: #fff;
    border-bottom: 1px solid #eaeaea;

    .chapter-header-main {
      display: flex;
      align-items: center;
    }
  }
}

.chapter-header {
  display: flex;
  justify-content: space-between;

  .chapter-header-main {
    display: flex;
  }

  .chapter-header-right {
    margin-left: 44px;
  }
}

.chapter-list {
  padding-bottom: 20px;
  background-color: #fff;

  .el-tree-node__content {
    background-color: #eaeaea;
    padding: 20px 30px;
    height: auto;
    display: block;

    .chapter-list-item {
      padding-left: 60px;
      margin-top: -25px;

      .charter-list-header {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .charter-list-content {
      display: flex;
      justify-content: space-between;
    }
  }
}

.recycleList {
  .recycleList-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;

    .recycleList-item-header {
      background-color: #daeaf7;
      padding: 10px 20px;
      border-bottom: 1px solid #ddd;
      display: flex;
      border-radius: 5px 5px 0 0;
      justify-content: space-between;

      .recycleList-item-header-right {
        color: #0966b4;
        cursor: pointer;
      }
    }
  }

  .recycleList-item-content {
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;

    .recycleList-item-content-right {
      color: #0966b4;
      cursor: pointer;
    }
  }
}


:deep(.pagination-container) {

  position: relative !important;

}
</style>
